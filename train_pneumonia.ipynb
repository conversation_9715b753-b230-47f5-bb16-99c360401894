# train_pneumonia_hardcoded_commented.py
# =====================================
# Fully hardcoded training script for pneumonia detection using Chest X-Ray images.
# This version includes detailed comments explaining each block of code.

import os  # For filesystem operations (creating directories, path handling)
import numpy as np  # For numerical operations on arrays
import matplotlib.pyplot as plt  # For plotting confusion matrix & ROC curve
import seaborn as sns  # For enhanced visualizations of the confusion matrix
from tqdm import tqdm  # For progress bars during training/validation loops

import torch  # PyTorch core
import torch.nn as nn  # Neural network modules (layers, loss functions)
import torch.optim as optim  # Optimization algorithms (Adam, SGD, etc.)
import torchvision  # Vision utilities: models, datasets, transforms
from torchvision import transforms, datasets, models  # Specific torchvision submodules
from torch.utils.data import DataLoader  # For batching and shuffling datasets

from sklearn.metrics import (
    classification_report,  # Detailed precision/recall/F1 per class
    confusion_matrix,      # To compute the confusion matrix
    roc_auc_score,         # To compute the Area Under ROC Curve
    roc_curve              # To compute ROC curve points
)

# ====================================================
#                  H A R D-CODED SETTINGS
# ----------------------------------------------------
# All paths and hyperparameters are defined here so that
# no command-line arguments are necessary when running.
# ====================================================

# 1) Path to the main dataset directory containing `train`, `val`, and `test` subfolders:
DATA_DIR = r"C:\Users\<USER>\Desktop\projects\DNN\Chest X-Ray Images (Pneumonia)\chest_xray"

# 2) Training hyperparameters:
BATCH_SIZE    = 32     # Number of samples per gradient update
EPOCHS        = 10     # Total number of training epochs
LEARNING_RATE = 1e-4   # Initial learning rate for the optimizer
NUM_WORKERS   = 4      # Number of subprocesses for data loading (per DataLoader)

# 3) Device selection: automatically use GPU if available, else CPU
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 4) Directory to save generated plots (confusion matrix, ROC curve)
PLOT_DIR = r"C:\Users\<USER>\Desktop\projects\DNN\results\plots"

# 5) Filepath to save the best model checkpoint during training
CHECKPOINT_PATH = r"C:\Users\<USER>\Desktop\projects\DNN\results\best_model.pth"

# Ensure the directories for plots and checkpoints exist (create if missing)
os.makedirs(PLOT_DIR, exist_ok=True)
os.makedirs(os.path.dirname(CHECKPOINT_PATH), exist_ok=True)


def get_data_loaders(data_dir, batch_size, num_workers):
    """
    Prepare DataLoaders for training, validation, and testing.
    - Applies appropriate image transforms for each split.
    - Returns DataLoaders and the underlying datasets (for class labels).
    """
    # Mean and standard deviation for single-channel (grayscale) images
    MEAN = [0.485]
    STD  = [0.229]

    # Define image augmentations + preprocessing for training:
    train_tfms = transforms.Compose([
        transforms.Grayscale(num_output_channels=1),  # Convert RGB to 1-channel
        transforms.Resize((224, 224)),               # Resize to 224×224 for ResNet
        transforms.RandomHorizontalFlip(),           # Data augmentation
        transforms.RandomRotation(10),               # Small random rotations
        transforms.ToTensor(),                       # Convert PIL image to PyTorch Tensor
        transforms.Normalize(MEAN, STD),             # Normalize pixel values
    ])

    # Validation and test transforms: no augmentation, only resizing + normalization
    val_tfms = transforms.Compose([
        transforms.Grayscale(num_output_channels=1),
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(MEAN, STD),
    ])

    # Create ImageFolder datasets by scanning directory structure
    train_ds = datasets.ImageFolder(os.path.join(data_dir, "train"), transform=train_tfms)
    val_ds   = datasets.ImageFolder(os.path.join(data_dir, "val"),   transform=val_tfms)
    test_ds  = datasets.ImageFolder(os.path.join(data_dir, "test"),  transform=val_tfms)

    # Wrap datasets in DataLoaders for batching, shuffling, and parallel loading
    train_dl = DataLoader(
        train_ds,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True  # Speed up GPU transfers if using CUDA
    )
    val_dl = DataLoader(
        val_ds,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    test_dl = DataLoader(
        test_ds,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )

    # Return DataLoaders and datasets for later use (e.g., getting class names)
    return train_dl, val_dl, test_dl, train_ds, val_ds, test_ds


def build_model(device):
    """
    Load a pretrained ResNet-18 model, modify it for single-channel input,
    adjust the final layer for 2 output classes (Pneumonia vs Normal),
    and move it to the target device (CPU/GPU).
    """
    # Load ResNet-18 with pretrained weights on ImageNet
    model = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)

    # Modify the first convolution to accept 1-channel (instead of 3) input
    model.conv1 = nn.Conv2d(
        in_channels=1,   # Single-channel (grayscale)
        out_channels=64,
        kernel_size=7,
        stride=2,
        padding=3,
        bias=False
    )

    # Replace final fully-connected layer: 512 features → 2 classes
    num_features = model.fc.in_features
    model.fc = nn.Linear(num_features, 2)

    # Move model to the specified device (GPU if available)
    return model.to(device)


def train_one_epoch(model, dataloader, criterion, optimizer, device):
    """
    Train the model for one epoch over the training dataset.
    - Tracks running loss and accuracy.
    - Uses tqdm for a progress bar visual.
    """
    model.train()  # Set model to training mode (enables dropout, batchnorm updates)
    running_loss = 0.0
    correct = 0
    total = 0

    # tqdm loop for progress visualization
    for images, labels in tqdm(dataloader, desc="Training", leave=False):
        # Move data to the target device
        images = images.to(device, non_blocking=True)
        labels = labels.to(device, non_blocking=True)

        # Forward pass
        outputs = model(images)
        loss = criterion(outputs, labels)

        # Backpropagation and optimization step
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # Accumulate loss and correct predictions for reporting
        batch_size = images.size(0)
        running_loss += loss.item() * batch_size
        preds = outputs.argmax(dim=1)
        correct += (preds == labels).sum().item()
        total += batch_size

        # Update tqdm description with current loss and accuracy
        tqdm.write(f"Batch loss: {loss.item():.4f}, Acc: {(correct/total):.3%}")

    # Compute average loss and accuracy over the epoch
    avg_loss = running_loss / total
    accuracy = correct / total
    return avg_loss, accuracy


def validate_one_epoch(model, dataloader, criterion, device):
    """
    Evaluate model performance on the validation set for one epoch.
    - Behaves similarly to train but without weight updates.
    """
    model.eval()  # Set model to evaluation mode (disables dropout, batchnorm updates)
    running_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():  # Disable gradient computations
        for images, labels in tqdm(dataloader, desc="Validation", leave=False):
            images = images.to(device, non_blocking=True)
            labels = labels.to(device, non_blocking=True)

            outputs = model(images)
            loss = criterion(outputs, labels)

            # Update metrics
            batch_size = images.size(0)
            running_loss += loss.item() * batch_size
            preds = outputs.argmax(dim=1)
            correct += (preds == labels).sum().item()
            total += batch_size

    avg_loss = running_loss / total
    accuracy = correct / total
    return avg_loss, accuracy


def evaluate_test_set(model, dataloader, dataset, device, plot_dir):
    """
    After training, evaluate the final model on the test set.
    - Prints a classification report to stdout.
    - Generates and saves a confusion matrix plot.
    - Generates and saves an ROC curve plot with AUC score.
    """
    model.eval()
    y_true = []  # Ground-truth labels
    y_prob = []  # Predicted probabilities for the positive class

    with torch.no_grad():
        for images, labels in tqdm(dataloader, desc="Testing", leave=False):
            images = images.to(device, non_blocking=True)
            outputs = model(images)
            # Compute softmax probabilities and take probability of class "1"
            probs = torch.softmax(outputs, dim=1)[:, 1].cpu().numpy()
            y_prob.extend(probs)
            y_true.extend(labels.cpu().numpy())

    y_true = np.array(y_true)
    y_pred = (np.array(y_prob) > 0.5).astype(int)  # Threshold at 0.5 for binary preds

    # Print detailed classification metrics
    print("\n======== Test Set Classification Report ========")
    print(classification_report(y_true, y_pred, target_names=dataset.classes))

    # ----- Confusion Matrix -----
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(6, 5))
    sns.heatmap(
        cm,
        annot=True,
        fmt="d",
        cmap="Blues",
        xticklabels=dataset.classes,
        yticklabels=dataset.classes
    )
    plt.xlabel("Predicted Label")
    plt.ylabel("True Label")
    plt.title("Confusion Matrix")
    cm_path = os.path.join(plot_dir, "confusion_matrix.png")
    plt.savefig(cm_path, dpi=150)
    plt.close()
    print(f"Saved confusion matrix to: {cm_path}")

    # ----- ROC Curve & AUC -----
    fpr, tpr, _ = roc_curve(y_true, y_prob)
    auc_score = roc_auc_score(y_true, y_prob)

    plt.figure(figsize=(6, 5))
    plt.plot(fpr, tpr, label=f"AUC = {auc_score:.3f}")
    plt.plot([0, 1], [0, 1], linestyle="--", color="gray")  # Diagonal random guess line
    plt.xlabel("False Positive Rate")
    plt.ylabel("True Positive Rate")
    plt.title("ROC Curve")
    plt.legend(loc="lower right")
    roc_path = os.path.join(plot_dir, "roc_curve.png")
    plt.savefig(roc_path, dpi=150)
    plt.close()
    print(f"Saved ROC curve to: {roc_path}\n")


def main():
    """
    Main function:
    1) Checks dataset structure.
    2) Prepares DataLoaders.
    3) Builds and configures the model.
    4) Runs training and validation loops.
    5) Saves best-performing model checkpoint.
    6) Loads best model and evaluates on test set.
    """
    # 1) Verify that `train`, `val`, and `test` subfolders exist
    expected_subdirs = ["train", "val", "test"]
    for sub in expected_subdirs:
        folder_path = os.path.join(DATA_DIR, sub)
        if not os.path.isdir(folder_path):
            raise FileNotFoundError(
                f"Expected folder `{sub}` under `{DATA_DIR}`, but it was not found."
            )

    # 2) Create DataLoaders for all splits
    train_dl, val_dl, test_dl, train_ds, val_ds, test_ds = get_data_loaders(
        DATA_DIR, BATCH_SIZE, NUM_WORKERS
    )

    # 3) Instantiate model, loss function, and optimizer
    model = build_model(DEVICE)
    criterion = nn.CrossEntropyLoss()  # Suitable for multi-class (binary) classification
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    best_val_acc = 0.0  # Track best validation accuracy

    # 4) Training loop over the specified number of epochs
    for epoch in range(1, EPOCHS + 1):
        print(f"===== Epoch {epoch}/{EPOCHS} =====")
        train_loss, train_acc = train_one_epoch(model, train_dl, criterion, optimizer, DEVICE)
        print(f"Train   → loss: {train_loss:.4f}, acc: {train_acc:.3%}")

        val_loss, val_acc = validate_one_epoch(model, val_dl, criterion, DEVICE)
        print(f"Validate→ loss: {val_loss:.4f}, acc: {val_acc:.3%}")

        # 5) Save checkpoint if we achieve new best validation accuracy
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), CHECKPOINT_PATH)
            print(f"→ New best val accuracy: {best_val_acc:.3%}. Checkpoint saved to `{CHECKPOINT_PATH}`.")

    print(f"\nTraining complete. Best validation accuracy = {best_val_acc:.3%}")

    # 6) Load the best model checkpoint for final evaluation
    print("\nLoading best model for test evaluation...")
    model.load_state_dict(torch.load(CHECKPOINT_PATH, map_location=DEVICE))
    model = model.to(DEVICE)

    # 7) Evaluate on test set and generate reports/plots
    evaluate_test_set(model, test_dl, test_ds, DEVICE, PLOT_DIR)


if __name__ == "__main__":
    main()


# train_pneumonia_full_visuals.py

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm

import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms, datasets, models
from torch.utils.data import DataLoader

from sklearn.metrics import (
    classification_report,
    confusion_matrix,
    roc_auc_score,
    roc_curve
)

# For Grad-CAM
from pytorch_grad_cam import GradCAM
from pytorch_grad_cam.utils.image import show_cam_on_image

# ====================================================
#                  H A R D-CODED SETTINGS
# ====================================================
# Use the current working directory as base
BASE_DIR = os.getcwd()

DATA_DIR = r"C:\Users\<USER>\Desktop\projects\DNN\Chest X-Ray Images (Pneumonia)\chest_xray"

BATCH_SIZE      = 32
EPOCHS          = 10
LEARNING_RATE   = 1e-4
NUM_WORKERS     = 4
DEVICE          = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# All outputs go into BASE_DIR
PLOT_DIR        = BASE_DIR
CHECKPOINT_PATH = os.path.join(BASE_DIR, "best_model.pth")

os.makedirs(PLOT_DIR, exist_ok=True)


# ====================================================
#               DATA LOADING & PLOTTING
# ====================================================
def get_data_loaders(data_dir, batch_size, num_workers):
    MEAN, STD = [0.485], [0.229]
    train_tfms = transforms.Compose([
        transforms.Grayscale(1),
        transforms.Resize((224,224)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(10),
        transforms.ToTensor(),
        transforms.Normalize(MEAN, STD),
    ])
    val_tfms = transforms.Compose([
        transforms.Grayscale(1),
        transforms.Resize((224,224)),
        transforms.ToTensor(),
        transforms.Normalize(MEAN, STD),
    ])

    train_ds = datasets.ImageFolder(os.path.join(data_dir, "train"), transform=train_tfms)
    val_ds   = datasets.ImageFolder(os.path.join(data_dir, "val"),   transform=val_tfms)
    test_ds  = datasets.ImageFolder(os.path.join(data_dir, "test"),  transform=val_tfms)

    train_dl = DataLoader(train_ds, batch_size=batch_size, shuffle=True,
                          num_workers=num_workers, pin_memory=True)
    val_dl   = DataLoader(val_ds,   batch_size=batch_size, shuffle=False,
                          num_workers=num_workers, pin_memory=True)
    test_dl  = DataLoader(test_ds,  batch_size=batch_size, shuffle=False,
                          num_workers=num_workers, pin_memory=True)

    return train_dl, val_dl, test_dl, train_ds, val_ds, test_ds


def plot_class_distribution(dataset, split_name, plot_dir):
    counts = np.bincount(dataset.targets)
    labels = dataset.classes
    plt.figure(figsize=(4,3))
    plt.bar(labels, counts)
    plt.title(f"{split_name.capitalize()} class counts")
    plt.ylabel("Number of samples")
    fname = os.path.join(plot_dir, f"{split_name}_class_distribution.png")
    plt.savefig(fname, dpi=150)
    plt.close()
    print(f"→ Saved {fname}")


# ====================================================
#                  MODEL DEFINITION
# ====================================================
def build_model(device):
    model = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
    model.conv1 = nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)
    model.fc    = nn.Linear(model.fc.in_features, 2)
    return model.to(device)


# ====================================================
#              TRAINING / VALIDATION
# ====================================================
def train_one_epoch(model, loader, criterion, optimizer, device):
    model.train()
    running_loss = running_correct = total = 0
    for x, y in tqdm(loader, desc="Train", leave=False):
        x, y = x.to(device), y.to(device)
        out = model(x)
        loss = criterion(out, y)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        running_loss += loss.item() * x.size(0)
        running_correct += (out.argmax(1) == y).sum().item()
        total += x.size(0)
    return running_loss/total, running_correct/total


def validate_one_epoch(model, loader, criterion, device):
    model.eval()
    running_loss = running_correct = total = 0
    with torch.no_grad():
        for x, y in tqdm(loader, desc="Val", leave=False):
            x, y = x.to(device), y.to(device)
            out = model(x)
            loss = criterion(out, y)
            running_loss += loss.item() * x.size(0)
            running_correct += (out.argmax(1) == y).sum().item()
            total += x.size(0)
    return running_loss/total, running_correct/total


# ====================================================
#              TEST EVALUATION & PLOTTING
# ====================================================
def evaluate_test_set(model, loader, dataset, device, plot_dir):
    model.eval()
    y_true, y_prob = [], []
    with torch.no_grad():
        for x, y in tqdm(loader, desc="Test", leave=False):
            x = x.to(device)
            out = model(x)
            probs = torch.softmax(out, dim=1)[:, 1].cpu().numpy()
            y_prob.extend(probs)
            y_true.extend(y.numpy())
    y_true = np.array(y_true)
    y_pred = (np.array(y_prob) > 0.5).astype(int)

    print("\n=== Classification Report ===")
    print(classification_report(y_true, y_pred, target_names=dataset.classes))

    # Confusion Matrix
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(5,4))
    sns.heatmap(cm, annot=True, fmt="d",
                xticklabels=dataset.classes,
                yticklabels=dataset.classes)
    plt.xlabel("Predicted"); plt.ylabel("True"); plt.title("Confusion Matrix")
    cm_path = os.path.join(plot_dir, "confusion_matrix.png")
    plt.savefig(cm_path, dpi=150); plt.close()
    print(f"→ Saved {cm_path}")

    # ROC Curve
    fpr, tpr, _ = roc_curve(y_true, y_prob)
    auc_score   = roc_auc_score(y_true, y_prob)
    plt.figure(figsize=(5,4))
    plt.plot(fpr, tpr, label=f"AUC = {auc_score:.3f}")
    plt.plot([0,1], [0,1], "--", color="gray")
    plt.xlabel("False Positive Rate"); plt.ylabel("True Positive Rate")
    plt.title("ROC Curve"); plt.legend(loc="lower right")
    roc_path = os.path.join(plot_dir, "roc_curve.png")
    plt.savefig(roc_path, dpi=150); plt.close()
    print(f"→ Saved {roc_path}")


# ====================================================
#                ORIGINAL TWIST: GRAD-CAM
# ====================================================
def generate_gradcam(model, loader, device, plot_dir, examples=6):
    model.eval()
    cam = GradCAM(model=model, target_layer=model.layer4[-1],
                  use_cuda=(device.type=="cuda"))
    gradcam_dir = os.path.join(plot_dir, "gradcam")
    os.makedirs(gradcam_dir, exist_ok=True)

    count = 0
    for x, y in loader:
        x = x.to(device)
        for i in range(x.size(0)):
            if count >= examples:
                return
            inp = x[i].unsqueeze(0)
            mask = cam(input_tensor=inp, target_category=y[i].item())[0]
            img = x[i].cpu().numpy().transpose(1,2,0)
            img = (img - img.min()) / (img.max() - img.min())
            cam_img = show_cam_on_image(img, mask, use_rgb=False)
            label = loader.dataset.classes[y[i].item()]
            fname = os.path.join(gradcam_dir, f"{count:02d}_{label}.png")
            plt.imsave(fname, cam_img)
            print(f"→ GradCAM saved: {fname}")
            count += 1


# ====================================================
#                         MAIN
# ====================================================
def main():
    # 1) Check directory structure
    for sub in ["train", "val", "test"]:
        if not os.path.isdir(os.path.join(DATA_DIR, sub)):
            raise FileNotFoundError(f"Missing '{sub}' in {DATA_DIR}")

    # 2) Load data
    train_dl, val_dl, test_dl, train_ds, val_ds, test_ds = \
        get_data_loaders(DATA_DIR, BATCH_SIZE, NUM_WORKERS)

    # 3) Plot class distributions
    plot_class_distribution(train_ds, "train", PLOT_DIR)
    plot_class_distribution(val_ds,   "val",   PLOT_DIR)
    plot_class_distribution(test_ds,  "test",  PLOT_DIR)

    # 4) Build model, criterion, optimizer
    model     = build_model(DEVICE)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    # 5) Training loop with history
    train_losses, train_accs = [], []
    val_losses,   val_accs   = [], []
    best_val_acc = 0.0

    for epoch in range(1, EPOCHS + 1):
        print(f"\nEpoch {epoch}/{EPOCHS}")
        tr_loss, tr_acc = train_one_epoch(model, train_dl, criterion, optimizer, DEVICE)
        print(f" Train → loss: {tr_loss:.4f}, acc: {tr_acc:.3%}")
        val_loss, val_acc = validate_one_epoch(model, val_dl, criterion, DEVICE)
        print(f"  Val  → loss: {val_loss:.4f}, acc: {val_acc:.3%}")

        train_losses.append(tr_loss)
        train_accs.append(tr_acc)
        val_losses.append(val_loss)
        val_accs.append(val_acc)

        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), CHECKPOINT_PATH)
            print(f" → Saved best model ({best_val_acc:.3%}) to {CHECKPOINT_PATH}")

    # 6) Plot training curves
    epochs_range = range(1, EPOCHS + 1)
    plt.figure(); plt.plot(epochs_range, train_losses, label="Train Loss")
    plt.plot(epochs_range, val_losses,   label="Val Loss"); plt.xlabel("Epoch")
    plt.ylabel("Loss"); plt.title("Loss vs Epoch"); plt.legend()
    plt.savefig(os.path.join(PLOT_DIR, "loss_curve.png"), dpi=150); plt.close()

    plt.figure(); plt.plot(epochs_range, train_accs, label="Train Acc")
    plt.plot(epochs_range, val_accs,    label="Val Acc"); plt.xlabel("Epoch")
    plt.ylabel("Accuracy"); plt.title("Accuracy vs Epoch"); plt.legend()
    plt.savefig(os.path.join(PLOT_DIR, "accuracy_curve.png"), dpi=150); plt.close()

    # 7) Final test evaluation
    model.load_state_dict(torch.load(CHECKPOINT_PATH, map_location=DEVICE))
    model.to(DEVICE)
    evaluate_test_set(model, test_dl, test_ds, DEVICE, PLOT_DIR)

    # 8) Generate Grad-CAM heatmaps
    print("\nGenerating Grad-CAM examples …")
    generate_gradcam(model, test_dl, DEVICE, PLOT_DIR, examples=6)


if __name__ == "__main__":
    main()


# # train_pneumonia_ensemble.py
# # ------------------------------------------------------------
# # Chest-X-Ray Pneumonia detection:
# #   – SE-ResNet18  +  MobileNetV3-Small
# #   – Soft-voting ensemble
# #   – Hard-coded paths / hyper-params
# #   – Generates: training curves, confusion matrix, ROC, Grad-CAMs
# # ------------------------------------------------------------

# import os, random, numpy as np, matplotlib.pyplot as plt, seaborn as sns
# from itertools import islice
# from pathlib import Path
# from tqdm import tqdm

# import torch, torch.nn as nn, torch.optim as optim
# from torch.utils.data import DataLoader
# from torchvision import transforms, datasets, models
# from sklearn.metrics import (classification_report, confusion_matrix,
#                              roc_auc_score, roc_curve)

# # Visual explanations
# from pytorch_grad_cam import GradCAM
# from pytorch_grad_cam.utils.image import show_cam_on_image

# # -------------------------
# # 1. HARD-CODED SETTINGS
# # -------------------------
# DATA_DIR = Path(r"C:\Users\<USER>\Desktop\projects\DNN\Chest X-Ray Images (Pneumonia)\chest_xray")
# RESULTS_DIR = Path(r"C:\Users\<USER>\Desktop\projects\DNN\results")
# PLOT_DIR = RESULTS_DIR / "plots"
# PLOT_DIR.mkdir(parents=True, exist_ok=True)

# SE_RESNET_CKPT  = RESULTS_DIR / "best_se_resnet18.pth"
# MNV3_CKPT       = RESULTS_DIR / "best_mobilenetv3.pth"

# BATCH_SIZE   = 32
# EPOCHS       = 10
# LR           = 1e-4
# NUM_WORKERS  = 4
# DEVICE       = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# SEED         = 42
# random.seed(SEED); np.random.seed(SEED); torch.manual_seed(SEED); torch.cuda.manual_seed_all(SEED)

# # -------------------------
# # 2. DATA LOADERS
# # -------------------------
# MEAN, STD = [0.485], [0.229]

# train_tfms = transforms.Compose([
#     transforms.Grayscale(1), transforms.Resize((224, 224)),
#     transforms.RandomHorizontalFlip(), transforms.RandomRotation(10),
#     transforms.ToTensor(), transforms.Normalize(MEAN, STD)
# ])
# val_tfms = transforms.Compose([
#     transforms.Grayscale(1), transforms.Resize((224, 224)),
#     transforms.ToTensor(), transforms.Normalize(MEAN, STD)
# ])

# train_ds = datasets.ImageFolder(DATA_DIR / "train", train_tfms)
# val_ds   = datasets.ImageFolder(DATA_DIR / "val",   val_tfms)
# test_ds  = datasets.ImageFolder(DATA_DIR / "test",  val_tfms)

# train_dl = DataLoader(train_ds, BATCH_SIZE, shuffle=True,  num_workers=NUM_WORKERS, pin_memory=True)
# val_dl   = DataLoader(val_ds,   BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS, pin_memory=True)
# test_dl  = DataLoader(test_ds,  BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS, pin_memory=True)

# # -------------------------
# # 3. MODEL DEFINITIONS
# # -------------------------
# class SEBlock(nn.Module):
#     """Squeeze-and-Excitation block."""
#     def __init__(self, channels, r=16):
#         super().__init__()
#         self.pool = nn.AdaptiveAvgPool2d(1)
#         self.fc   = nn.Sequential(
#             nn.Linear(channels, channels // r), nn.ReLU(True),
#             nn.Linear(channels // r, channels), nn.Sigmoid()
#         )
#     def forward(self, x):
#         b, c, _, _ = x.size()
#         y = self.pool(x).view(b, c)
#         y = self.fc(y).view(b, c, 1, 1)
#         return x * y

# def se_resnet18():
#     """ResNet-18 + SE after each bottleneck."""
#     base = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
#     # first conv → 1-channel
#     base.conv1 = nn.Conv2d(1, 64, 7, 2, 3, bias=False)
#     # inject SE blocks
#     def _add_se(layer):
#         for name, mod in layer.named_children():
#             if isinstance(mod, models.resnet.BasicBlock):
#                 mod.add_module("se", SEBlock(mod.bn2.num_features))
#     _add_se(base.layer1); _add_se(base.layer2); _add_se(base.layer3); _add_se(base.layer4)
#     # fc → 2 classes
#     base.fc = nn.Linear(base.fc.in_features, 2)
#     return base

# def mobilenetv3_small():
#     m = models.mobilenet_v3_small(weights=models.MobileNet_V3_Small_Weights.IMAGENET1K_V1)
#     # change first conv to 1-channel
#     first_conv = m.features[0][0]      # ConvBNAct
#     m.features[0][0] = nn.Conv2d(1, first_conv.out_channels,
#                                  kernel_size=first_conv.kernel_size,
#                                  stride=first_conv.stride,
#                                  padding=first_conv.padding,
#                                  bias=False)
#     m.classifier[-1] = nn.Linear(m.classifier[-1].in_features, 2)
#     return m

# model_se   = se_resnet18().to(DEVICE)
# model_mnv3 = mobilenetv3_small().to(DEVICE)

# criterion = nn.CrossEntropyLoss()
# opt_se   = optim.Adam(model_se.parameters(),   lr=LR)
# opt_mnv3 = optim.Adam(model_mnv3.parameters(), lr=LR)

# history = {"se": {"train_loss":[], "val_loss":[], "train_acc":[], "val_acc":[]},
#            "mnv3":{"train_loss":[], "val_loss":[], "train_acc":[], "val_acc":[]} }

# best_val_acc_se, best_val_acc_mnv3 = 0, 0

# # -------------------------
# # 4. TRAIN / VAL UTILITIES
# # -------------------------
# def run_epoch(model, loader, train, optimizer=None):
#     if train: model.train()
#     else:     model.eval()
#     loss_sum, correct, total = 0., 0, 0
#     with torch.set_grad_enabled(train):
#         for xb, yb in loader:
#             xb, yb = xb.to(DEVICE), yb.to(DEVICE)
#             out = model(xb)
#             loss = criterion(out, yb)
#             if train:
#                 optimizer.zero_grad(); loss.backward(); optimizer.step()
#             loss_sum += loss.item()*xb.size(0)
#             preds = out.argmax(1)
#             correct += (preds == yb).sum().item()
#             total += xb.size(0)
#     return loss_sum/total, correct/total

# # -------------------------
# # 5. TRAINING LOOP
# # -------------------------
# for epoch in range(1, EPOCHS+1):
#     print(f"\nEpoch {epoch}/{EPOCHS}")
#     # --- SE-ResNet18
#     tl, ta = run_epoch(model_se,   train_dl, True,  opt_se)
#     vl, va = run_epoch(model_se,   val_dl,   False)
#     history["se"]["train_loss"].append(tl); history["se"]["val_loss"].append(vl)
#     history["se"]["train_acc"].append(ta);  history["se"]["val_acc"].append(va)
#     if va > best_val_acc_se:
#         best_val_acc_se = va
#         torch.save(model_se.state_dict(), SE_RESNET_CKPT)
#     # --- MobileNetV3
#     tl, ta = run_epoch(model_mnv3, train_dl, True,  opt_mnv3)
#     vl, va = run_epoch(model_mnv3, val_dl,   False)
#     history["mnv3"]["train_loss"].append(tl); history["mnv3"]["val_loss"].append(vl)
#     history["mnv3"]["train_acc"].append(ta);  history["mnv3"]["val_acc"].append(va)
#     if va > best_val_acc_mnv3:
#         best_val_acc_mnv3 = va
#         torch.save(model_mnv3.state_dict(), MNV3_CKPT)
#     print(f"  SE-ResNet18  val_acc={va:.3%} | best={best_val_acc_se:.3%}")
#     print(f"  MobileNetV3  val_acc={best_val_acc_mnv3:.3%}")

# print("\nTraining done. Reloading best checkpoints …")
# model_se.load_state_dict(torch.load(SE_RESNET_CKPT,  map_location=DEVICE))
# model_mnv3.load_state_dict(torch.load(MNV3_CKPT, map_location=DEVICE))
# model_se.eval(); model_mnv3.eval()

# # -------------------------
# # 6. TEST-TIME ENSEMBLE EVAL
# # -------------------------
# y_true, y_prob_se, y_prob_mnv3 = [], [], []
# with torch.no_grad():
#     for xb, yb in tqdm(test_dl, desc="Test"):
#         xb = xb.to(DEVICE)
#         p_se   = torch.softmax(model_se(xb),   1)[:,1].cpu().numpy()
#         p_mnv3 = torch.softmax(model_mnv3(xb), 1)[:,1].cpu().numpy()
#         y_prob_se.extend(p_se);  y_prob_mnv3.extend(p_mnv3)
#         y_true.extend(yb.numpy())

# y_true = np.array(y_true)
# prob_se   = np.array(y_prob_se)
# prob_mnv3 = np.array(y_prob_mnv3)
# prob_ens  = (prob_se + prob_mnv3) / 2.0
# y_pred_ens = (prob_ens > 0.5).astype(int)

# print("\n=== Ensemble Classification Report ===")
# print(classification_report(y_true, y_pred_ens, target_names=test_ds.classes))

# # -------------------------
# # 7. VISUALS
# # -------------------------
# # 7.1 Training curves
# def plot_history(key, title):
#     epochs = range(1, EPOCHS+1)
#     plt.figure(figsize=(6,4))
#     plt.plot(epochs, history[key]["train_loss"], label="train_loss")
#     plt.plot(epochs, history[key]["val_loss"],   label="val_loss")
#     plt.twinx()
#     plt.plot(epochs, history[key]["train_acc"],  "g--", label="train_acc")
#     plt.plot(epochs, history[key]["val_acc"],    "r--", label="val_acc")
#     plt.title(title); plt.xlabel("Epoch"); plt.legend(loc="center left")
#     plt.tight_layout(); plt.savefig(PLOT_DIR/f"{key}_curves.png", dpi=150); plt.close()

# plot_history("se",   "SE-ResNet18 Training-Val Curves")
# plot_history("mnv3", "MobileNetV3 Training-Val Curves")

# # 7.2 Confusion matrix
# cm = confusion_matrix(y_true, y_pred_ens)
# plt.figure(figsize=(4,4))
# sns.heatmap(cm, annot=True, fmt="d", cmap="Blues",
#             xticklabels=test_ds.classes, yticklabels=test_ds.classes)
# plt.xlabel("Predicted"); plt.ylabel("True"); plt.title("Ensemble Confusion Matrix")
# plt.tight_layout(); plt.savefig(PLOT_DIR/"ensemble_confusion_matrix.png", dpi=150); plt.close()

# # 7.3 ROC curve
# fpr, tpr, _ = roc_curve(y_true, prob_ens)
# auc = roc_auc_score(y_true, prob_ens)
# plt.figure(figsize=(4,4))
# plt.plot(fpr, tpr, label=f"AUC={auc:.3f}")
# plt.plot([0,1], [0,1], "--", color="gray")
# plt.xlabel("False Positive Rate"); plt.ylabel("True Positive Rate")
# plt.title("Ensemble ROC Curve"); plt.legend(loc="lower right")
# plt.tight_layout(); plt.savefig(PLOT_DIR/"ensemble_roc.png", dpi=150); plt.close()

# # 7.4 Grad-CAM overlays (save three images)
# gradcam_dir = PLOT_DIR / "gradcam"
# gradcam_dir.mkdir(exist_ok=True)
# target_layers = [model_se.layer4[-1].se]  # use SE layer for CAM
# cam = GradCAM(model_se, target_layers=target_layers)
# samples_shown = 0
# for idx, (img, label) in enumerate(islice(test_ds, 200)):   # scan first 200 images
#     if samples_shown >= 3: break
#     if (label == 0 and samples_shown == 0) or (label == 1):
#         # prepare tensor
#         x = val_tfms(img).unsqueeze(0).to(DEVICE)
#         grayscale_cam = cam(x)[0]         # (H,W)
#         rgb_img = np.repeat(np.array(img.resize((224,224)))[:, :, None]/255.0, 3, axis=2)
#         vis = show_cam_on_image(rgb_img, grayscale_cam, use_rgb=True)
#         plt.imsave(gradcam_dir/f"cam_{idx}_label{label}.png", vis)
#         samples_shown += 1

# print(f"\nAll plots saved under {PLOT_DIR}")


# train_pneumonia_ensemble_commented.py
# ====================================================
# Pneumonia Detection Ensemble Script with Detailed Explanation
# ----------------------------------------------------
# This script trains two convolutional neural networks (SE-ResNet18 and
# MobileNetV3-Small) on chest X-ray images and performs a soft-voting
# ensemble at inference. It also generates training/validation curves,
# confusion matrix, ROC curve, and Grad-CAM visualizations to interpret
# model focus regions.
# All paths and hyperparameters are hard-coded for simplicity.
# ====================================================

import os                # Filesystem operations
import random            # For seeding reproducibility
import numpy as np       # Numerical operations
import matplotlib.pyplot as plt  # Plotting utilities
import seaborn as sns    # Advanced visualization (heatmaps)
from itertools import islice  # To slice iterators (sampling)
from pathlib import Path  # Object-oriented filesystem paths
from tqdm import tqdm     # Progress bars

import torch              # Core PyTorch
import torch.nn as nn     # Neural network modules
import torch.optim as optim  # Optimization algorithms
from torch.utils.data import DataLoader  # DataLoader abstraction
from torchvision import transforms, datasets, models  # Vision utilities

# Metrics from scikit-learn
from sklearn.metrics import (
    classification_report,  # Detailed per-class metrics
    confusion_matrix,      # Confusion matrix computation
    roc_auc_score,         # ROC AUC calculation
    roc_curve              # ROC curve points
)

# Grad-CAM for visual explanations
from pytorch_grad_cam import GradCAM
from pytorch_grad_cam.utils.image import show_cam_on_image

# -------------------------
# 1. HARD-CODED SETTINGS
# -------------------------
# Define all file paths, hyperparameters, and reproducibility seeds here.
DATA_DIR     = Path(r"C:\Users\<USER>\Desktop\projects\DNN\Chest X-Ray Images (Pneumonia)\chest_xray")
RESULTS_DIR  = Path(r"C:\Users\<USER>\Desktop\projects\DNN\results")
PLOT_DIR     = RESULTS_DIR / "plots"
# Create plots directory if it doesn't exist (including parents)
PLOT_DIR.mkdir(parents=True, exist_ok=True)

# Paths to save best model weights
SE_RESNET_CKPT = RESULTS_DIR / "best_se_resnet18.pth"
MNV3_CKPT      = RESULTS_DIR / "best_mobilenetv3.pth"

# Model training hyperparameters
BATCH_SIZE  = 32        # Number of images per batch
EPOCHS      = 10        # Total training epochs
LR          = 1e-4      # Learning rate for optimizers
NUM_WORKERS = 4         # DataLoader subprocesses

# Device: use GPU if available, else CPU
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Seed everything for deterministic behavior (as much as possible)
SEED = 42
random.seed(SEED)
np.random.seed(SEED)
torch.manual_seed(SEED)
torch.cuda.manual_seed_all(SEED)

# -------------------------
# 2. DATA LOADING & PREPROCESSING
# -------------------------
# Define normalization constants for single-channel images
MEAN, STD = [0.485], [0.229]

# Compose training transformations:
#  - Convert to grayscale
#  - Resize to 224×224
#  - Random flips and rotations for augmentation
#  - Convert to tensor and normalize
train_tfms = transforms.Compose([
    transforms.Grayscale(1),          # Ensure single channel
    transforms.Resize((224, 224)),    # Match input size of models
    transforms.RandomHorizontalFlip(),
    transforms.RandomRotation(10),
    transforms.ToTensor(),            # PIL → Tensor
    transforms.Normalize(MEAN, STD)   # Standardize pixel values
])
# Validation/test transformations: identical but without augmentation
val_tfms = transforms.Compose([
    transforms.Grayscale(1),
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(MEAN, STD)
])

# Create datasets by scanning directory structure: train/val/test
train_ds = datasets.ImageFolder(DATA_DIR / "train", train_tfms)
val_ds   = datasets.ImageFolder(DATA_DIR / "val",   val_tfms)
test_ds  = datasets.ImageFolder(DATA_DIR / "test",  val_tfms)

# Wrap datasets into DataLoaders for batching and shuffling
train_dl = DataLoader(train_ds, BATCH_SIZE, shuffle=True,  num_workers=NUM_WORKERS, pin_memory=True)
val_dl   = DataLoader(val_ds,   BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS, pin_memory=True)
test_dl  = DataLoader(test_ds,  BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS, pin_memory=True)

# -------------------------
# 3. MODEL DEFINITIONS
# -------------------------
# (a) Squeeze-and-Excitation block to recalibrate channel-wise features
class SEBlock(nn.Module):
    def __init__(self, channels, reduction=16):
        super().__init__()
        # Global average pooling to summarize each channel
        self.pool = nn.AdaptiveAvgPool2d(1)
        # Two FC layers: reduction -> expansion + sigmoid gating
        self.fc   = nn.Sequential(
            nn.Linear(channels, channels // reduction),
            nn.ReLU(True),
            nn.Linear(channels // reduction, channels),
            nn.Sigmoid()
        )
    def forward(self, x):
        b, c, _, _ = x.size()           # Batch size & channel count
        y = self.pool(x).view(b, c)    # Shape → (b, c)
        y = self.fc(y).view(b, c, 1, 1) # Shape → (b, c, 1, 1)
        return x * y                   # Scale original features

# (b) SE-ResNet18: inject SEBlock after each BasicBlock
def se_resnet18():
    # Load pretrained ResNet-18
    base = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
    # Modify the first convolution to accept single-channel input
    base.conv1 = nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)

    # Helper to insert SEBlock into each BasicBlock
    def _add_se(layer):
        for name, module in layer.named_children():
            # Identify BasicBlock instances
            if isinstance(module, models.resnet.BasicBlock):
                # Add SE module after second batchnorm
                module.add_module("se", SEBlock(module.bn2.num_features))
    # Inject into all four residual layers
    _add_se(base.layer1)
    _add_se(base.layer2)
    _add_se(base.layer3)
    _add_se(base.layer4)

    # Replace final fully-connected layer to output 2 classes
    base.fc = nn.Linear(base.fc.in_features, 2)
    return base

# (c) MobileNetV3-Small: adjust input and output channels
def mobilenetv3_small():
    m = models.mobilenet_v3_small(weights=models.MobileNet_V3_Small_Weights.IMAGENET1K_V1)
    # Change first conv layer for single-channel input
    first_conv = m.features[0][0]  # Initial ConvBNAct block
    m.features[0][0] = nn.Conv2d(
        1,
        first_conv.out_channels,
        kernel_size=first_conv.kernel_size,
        stride=first_conv.stride,
        padding=first_conv.padding,
        bias=False
    )
    # Adjust final classifier head for 2 classes
    m.classifier[-1] = nn.Linear(m.classifier[-1].in_features, 2)
    return m

# Instantiate models and move to DEVICE
model_se   = se_resnet18().to(DEVICE)
model_mnv3 = mobilenetv3_small().to(DEVICE)

# Loss function and separate optimizers for each model
criterion = nn.CrossEntropyLoss()
opt_se    = optim.Adam(model_se.parameters(),   lr=LR)
opt_mnv3  = optim.Adam(model_mnv3.parameters(), lr=LR)

# Dictionaries to track training history
history = {
    "se":   {"train_loss":[], "val_loss":[], "train_acc":[], "val_acc":[]},
    "mnv3": {"train_loss":[], "val_loss":[], "train_acc":[], "val_acc":[]}
}

# Variables to store best validation accuracy for checkpointing
best_val_acc_se, best_val_acc_mnv3 = 0.0, 0.0

# -------------------------
# 4. TRAINING & VALIDATION FUNCTION
# -------------------------
def run_epoch(model, loader, is_train, optimizer=None):
    """
    Single epoch routine for training or validation.
    - model: the neural network
    - loader: DataLoader
    - is_train: True for training, False for validation
    - optimizer: required if is_train == True
    Returns average loss and accuracy over the epoch.
    """
    # Set mode accordingly
    model.train() if is_train else model.eval()

    total_loss, correct, total = 0.0, 0, 0
    # Gradient tracking only if training
    with torch.set_grad_enabled(is_train):
        for xb, yb in loader:
            xb, yb = xb.to(DEVICE), yb.to(DEVICE)
            outputs = model(xb)
            loss    = criterion(outputs, yb)

            if is_train:
                optimizer.zero_grad()   # Clear old gradients
                loss.backward()         # Backpropagate
                optimizer.step()        # Update weights

            # Accumulate metrics
            batch_size     = xb.size(0)
            total_loss    += loss.item() * batch_size
            preds          = outputs.argmax(dim=1)
            correct       += (preds == yb).sum().item()
            total         += batch_size

    avg_loss = total_loss / total
    accuracy = correct / total
    return avg_loss, accuracy

# -------------------------
# 5. MAIN TRAINING LOOP
# -------------------------
for epoch in range(1, EPOCHS + 1):
    print(f"\nEpoch {epoch}/{EPOCHS}")
    # Train & validate SE-ResNet18
    tl, ta = run_epoch(model_se,   train_dl, True,  opt_se)
    vl, va = run_epoch(model_se,   val_dl,   False)
    history["se"]["train_loss"].append(tl)
    history["se"]["val_loss"].append(vl)
    history["se"]["train_acc"].append(ta)
    history["se"]["val_acc"].append(va)
    # Save checkpoint if validation improves
    if va > best_val_acc_se:
        best_val_acc_se = va
        torch.save(model_se.state_dict(), SE_RESNET_CKPT)

    # Train & validate MobileNetV3-Small
    tl, ta = run_epoch(model_mnv3, train_dl, True,  opt_mnv3)
    vl, va = run_epoch(model_mnv3, val_dl,   False)
    history["mnv3"]["train_loss"].append(tl)
    history["mnv3"]["val_loss"].append(vl)
    history["mnv3"]["train_acc"].append(ta)
    history["mnv3"]["val_acc"].append(va)
    # Checkpoint MobileNetV3 if improved
    if va > best_val_acc_mnv3:
        best_val_acc_mnv3 = va
        torch.save(model_mnv3.state_dict(), MNV3_CKPT)

    # Print validation accuracies
    print(f"  SE-ResNet18   val_acc = {history['se']['val_acc'][-1]:.3%} (best {best_val_acc_se:.3%})")
    print(f"  MobileNetV3   val_acc = {history['mnv3']['val_acc'][-1]:.3%} (best {best_val_acc_mnv3:.3%})")

# After training, load best weights for both models
print("\nTraining complete. Loading best checkpoints...")
model_se.load_state_dict(torch.load(SE_RESNET_CKPT, map_location=DEVICE)); model_se.eval()
model_mnv3.load_state_dict(torch.load(MNV3_CKPT, map_location=DEVICE)); model_mnv3.eval()

# -------------------------
# 6. TEST-TIME ENSEMBLE EVALUATION
# -------------------------
# Collect true labels and predicted probabilities from both models
y_true, y_prob_se, y_prob_mnv3 = [], [], []
with torch.no_grad():
    for xb, yb in tqdm(test_dl, desc="Test Ensemble"):
        xb = xb.to(DEVICE)
        # Softmax to get probabilities
        p_se   = torch.softmax(model_se(xb),   dim=1)[:,1].cpu().numpy()
        p_mnv3 = torch.softmax(model_mnv3(xb), dim=1)[:,1].cpu().numpy()
        y_prob_se.extend(p_se)
        y_prob_mnv3.extend(p_mnv3)
        y_true.extend(yb.numpy())

# Convert lists to numpy arrays for metric computation
y_true    = np.array(y_true)
prob_se   = np.array(y_prob_se)
prob_mnv3 = np.array(y_prob_mnv3)
# Soft-voting ensemble: average probabilities
prob_ens  = (prob_se + prob_mnv3) / 2.0
# Binary predictions at threshold 0.5
y_pred_ens = (prob_ens > 0.5).astype(int)

# Print classification report for ensemble
print("\n=== Ensemble Classification Report ===")
print(classification_report(y_true, y_pred_ens, target_names=test_ds.classes))

# -------------------------
# 7. VISUALIZATIONS
# -------------------------
# 7.1 Plot training/validation curves for both models
def plot_history(key, title):
    """
    Plots loss and accuracy curves for given model history.
    - Loss on left y-axis
    - Accuracy on right y-axis
    """
    epochs = range(1, EPOCHS + 1)
    plt.figure(figsize=(6, 4))
    # Plot losses
    plt.plot(epochs, history[key]["train_loss"], label="train_loss")
    plt.plot(epochs, history[key]["val_loss"],   label="val_loss")
    # Overlay accuracy on twin axis
    ax2 = plt.gca().twinx()
    ax2.plot(epochs, history[key]["train_acc"], "g--", label="train_acc")
    ax2.plot(epochs, history[key]["val_acc"],   "r--", label="val_acc")
    plt.title(title)
    plt.xlabel("Epoch")
    plt.legend(loc="upper left")
    plt.tight_layout()
    plt.savefig(PLOT_DIR / f"{key}_curves.png", dpi=150)
    plt.close()

plot_history("se",   "SE-ResNet18 Training & Validation")
plot_history("mnv3", "MobileNetV3 Training & Validation")

# 7.2 Confusion Matrix for Ensemble Predictions
cm = confusion_matrix(y_true, y_pred_ens)
plt.figure(figsize=(4, 4))
sns.heatmap(
    cm, annot=True, fmt="d", cmap="Blues",
    xticklabels=test_ds.classes, yticklabels=test_ds.classes
)
plt.xlabel("Predicted Label")
plt.ylabel("True Label")
plt.title("Ensemble Confusion Matrix")
plt.tight_layout()
plt.savefig(PLOT_DIR / "ensemble_confusion_matrix.png", dpi=150)
plt.close()

# 7.3 ROC Curve & AUC for Ensemble
fpr, tpr, _ = roc_curve(y_true, prob_ens)
auc_score = roc_auc_score(y_true, prob_ens)
plt.figure(figsize=(4, 4))
plt.plot(fpr, tpr, label=f"AUC = {auc_score:.3f}")
plt.plot([0, 1], [0, 1], linestyle="--", color="gray")
plt.xlabel("False Positive Rate")
plt.ylabel("True Positive Rate")
plt.title("Ensemble ROC Curve")
plt.legend(loc="lower right")
plt.tight_layout()
plt.savefig(PLOT_DIR / "ensemble_roc.png", dpi=150)
plt.close()

# 7.4 Grad-CAM: visualize where SE-ResNet18 focuses
gradcam_dir = PLOT_DIR / "gradcam"
gradcam_dir.mkdir(exist_ok=True)
# Use the SE block in layer4 for activation maps
target_layers = [model_se.layer4[-1].se]
cam = GradCAM(model=model_se, target_layers=target_layers)

# Save Grad-CAM overlays for up to 3 samples (both classes)
samples_shown = 0
for idx, (img, label) in enumerate(islice(test_ds, 200)):
    if samples_shown >= 3:
        break
    # Choose first normal (label 0) and first pneumonia (label 1)
    if (label == 0 and samples_shown == 0) or (label == 1 and samples_shown == 1):
        # Prepare image tensor for model
        input_tensor = val_tfms(img).unsqueeze(0).to(DEVICE)
        grayscale_cam = cam(input_tensor)[0]  # H×W mask
        # Prepare original image as RGB normalized [0,1]
        rgb_img = np.repeat(np.array(img.resize((224,224)))[:, :, None]/255.0, 3, axis=2)
        # Overlay heatmap on image
        visualization = show_cam_on_image(rgb_img, grayscale_cam, use_rgb=True)
        # Save result
        plt.imsave(gradcam_dir / f"cam_{idx}_label{label}.png", visualization)
        samples_shown += 1

print(f"All plots and Grad-CAMs saved under: {PLOT_DIR}")


# train_pneumonia_ensemble.py
# ------------------------------------------------------------
# Chest-X-Ray Pneumonia detection
#   – SE-ResNet18  +  MobileNetV3-Small
#   – Soft-voting ensemble
#   – Hard-coded paths / hyper-params
#   – Generates: training curves, confusion matrix, ROC, Grad-CAMs
# ------------------------------------------------------------

import os, random, numpy as np, matplotlib.pyplot as plt, seaborn as sns
from itertools import islice
from pathlib import Path
from tqdm import tqdm

import torch, torch.nn as nn, torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms, datasets, models
from sklearn.metrics import (classification_report, confusion_matrix,
                             roc_auc_score, roc_curve)

# Visual explanations
from pytorch_grad_cam import GradCAM
from pytorch_grad_cam.utils.image import show_cam_on_image

# -------------------------
# 1. HARD-CODED SETTINGS
# -------------------------
DATA_DIR   = Path(r"C:\Users\<USER>\Desktop\projects\DNN\Chest X-Ray Images (Pneumonia)\chest_xray")
RESULTS    = Path(r"C:\Users\<USER>\Desktop\projects\DNN\results")
PLOT_DIR   = RESULTS / "plots"
PLOT_DIR.mkdir(parents=True, exist_ok=True)

SE_CKPT    = RESULTS / "best_se_resnet18.pth"
MNV3_CKPT  = RESULTS / "best_mobilenetv3.pth"

BATCH_SIZE  = 32
EPOCHS      = 10
LR          = 1e-4
NUM_WORKERS = 4
DEVICE      = torch.device("cuda" if torch.cuda.is_available() else "cpu")
SEED        = 42
random.seed(SEED); np.random.seed(SEED); torch.manual_seed(SEED); torch.cuda.manual_seed_all(SEED)

# -------------------------
# 2. DATA LOADERS
# -------------------------
MEAN, STD = [0.485], [0.229]

train_tfms = transforms.Compose([
    transforms.Grayscale(1), transforms.Resize((224, 224)),
    transforms.RandomHorizontalFlip(), transforms.RandomRotation(10),
    transforms.ToTensor(), transforms.Normalize(MEAN, STD)
])
val_tfms = transforms.Compose([
    transforms.Grayscale(1), transforms.Resize((224, 224)),
    transforms.ToTensor(), transforms.Normalize(MEAN, STD)
])

train_ds = datasets.ImageFolder(DATA_DIR / "train", train_tfms)
val_ds   = datasets.ImageFolder(DATA_DIR / "val",   val_tfms)
test_ds  = datasets.ImageFolder(DATA_DIR / "test",  val_tfms)

train_dl = DataLoader(train_ds, BATCH_SIZE, shuffle=True,  num_workers=NUM_WORKERS, pin_memory=True)
val_dl   = DataLoader(val_ds,   BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS, pin_memory=True)
test_dl  = DataLoader(test_ds,  BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS, pin_memory=True)

# -------------------------
# 3. MODEL DEFINITIONS
# -------------------------
class SEBlock(nn.Module):
    """Squeeze-and-Excitation block."""
    def __init__(self, channels, r=16):
        super().__init__()
        self.pool = nn.AdaptiveAvgPool2d(1)
        self.fc   = nn.Sequential(
            nn.Linear(channels, channels // r), nn.ReLU(True),
            nn.Linear(channels // r, channels), nn.Sigmoid()
        )
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y

def se_resnet18():
    """ResNet-18 + SE blocks."""
    base = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
    base.conv1 = nn.Conv2d(1, 64, 7, 2, 3, bias=False)           # grayscale
    def _add_se(layer):
        for _, mod in layer.named_children():
            if isinstance(mod, models.resnet.BasicBlock):
                mod.add_module("se", SEBlock(mod.bn2.num_features))
    _add_se(base.layer1); _add_se(base.layer2); _add_se(base.layer3); _add_se(base.layer4)
    base.fc = nn.Linear(base.fc.in_features, 2)
    return base

def mobilenetv3_small():
    m = models.mobilenet_v3_small(weights=models.MobileNet_V3_Small_Weights.IMAGENET1K_V1)
    first = m.features[0][0]
    m.features[0][0] = nn.Conv2d(1, first.out_channels,
                                 kernel_size=first.kernel_size,
                                 stride=first.stride,
                                 padding=first.padding,
                                 bias=False)
    m.classifier[-1] = nn.Linear(m.classifier[-1].in_features, 2)
    return m

model_se   = se_resnet18().to(DEVICE)
model_mnv3 = mobilenetv3_small().to(DEVICE)

criterion = nn.CrossEntropyLoss()
opt_se    = optim.Adam(model_se.parameters(),   lr=LR)
opt_mnv3  = optim.Adam(model_mnv3.parameters(), lr=LR)

history = {k: {m: [] for m in ["train_loss","val_loss","train_acc","val_acc"]}
           for k in ["se","mnv3"]}
best_val_acc_se, best_val_acc_mnv3 = 0., 0.

# -------------------------
# 4. TRAIN / VAL UTILITIES
# -------------------------
def run_epoch(model, loader, train, optimizer=None):
    model.train() if train else model.eval()
    loss_sum, correct, total = 0., 0, 0
    with torch.set_grad_enabled(train):
        for xb, yb in loader:
            xb, yb = xb.to(DEVICE), yb.to(DEVICE)
            out = model(xb)
            loss = criterion(out, yb)
            if train:
                optimizer.zero_grad(); loss.backward(); optimizer.step()
            loss_sum += loss.item()*xb.size(0)
            preds = out.argmax(1)
            correct += (preds == yb).sum().item()
            total += xb.size(0)
    return loss_sum/total, correct/total

# -------------------------
# 5. TRAINING LOOP
# -------------------------
for epoch in range(1, EPOCHS+1):
    print(f"\nEpoch {epoch}/{EPOCHS}")
    # SE-ResNet18
    tl, ta = run_epoch(model_se, train_dl, True, opt_se)
    vl, va = run_epoch(model_se, val_dl,   False)
    for k,v in zip(["train_loss","train_acc","val_loss","val_acc"], [tl,ta,vl,va]):
        history["se"][k].append(v)
    if va > best_val_acc_se:
        best_val_acc_se = va; torch.save(model_se.state_dict(), SE_CKPT)
    # MobileNetV3
    tl, ta = run_epoch(model_mnv3, train_dl, True, opt_mnv3)
    vl, va = run_epoch(model_mnv3, val_dl,   False)
    for k,v in zip(["train_loss","train_acc","val_loss","val_acc"], [tl,ta,vl,va]):
        history["mnv3"][k].append(v)
    if va > best_val_acc_mnv3:
        best_val_acc_mnv3 = va; torch.save(model_mnv3.state_dict(), MNV3_CKPT)
    print(f"  SE-ResNet18  val_acc={history['se']['val_acc'][-1]:.3%}  best={best_val_acc_se:.3%}")
    print(f"  MobileNetV3  val_acc={history['mnv3']['val_acc'][-1]:.3%}  best={best_val_acc_mnv3:.3%}")

print("\nTraining complete. Reloading best checkpoints …")
model_se.load_state_dict(torch.load(SE_CKPT,  map_location=DEVICE))
model_mnv3.load_state_dict(torch.load(MNV3_CKPT, map_location=DEVICE))
model_se.eval(); model_mnv3.eval()

# -------------------------
# 6. TEST-TIME ENSEMBLE
# -------------------------
y_true, prob_se, prob_mnv3 = [], [], []
with torch.no_grad():
    for xb, yb in tqdm(test_dl, desc="Test"):
        xb = xb.to(DEVICE)
        prob_se.extend(torch.softmax(model_se(xb),1)[:,1].cpu().numpy())
        prob_mnv3.extend(torch.softmax(model_mnv3(xb),1)[:,1].cpu().numpy())
        y_true.extend(yb.numpy())

y_true  = np.array(y_true)
prob_se = np.array(prob_se)
prob_mnv3 = np.array(prob_mnv3)
prob_ens  = (prob_se + prob_mnv3)/2
y_pred_ens = (prob_ens > 0.5).astype(int)

print("\n=== Ensemble Classification Report ===")
print(classification_report(y_true, y_pred_ens, target_names=test_ds.classes))

# -------------------------
# 7. VISUALS
# -------------------------
def plot_history(key, title):
    ep = range(1, EPOCHS+1)
    plt.figure(figsize=(6,4))
    plt.plot(ep, history[key]["train_loss"], label="train_loss")
    plt.plot(ep, history[key]["val_loss"],   label="val_loss")
    plt.twinx()
    plt.plot(ep, history[key]["train_acc"],  "g--", label="train_acc")
    plt.plot(ep, history[key]["val_acc"],    "r--", label="val_acc")
    plt.title(title); plt.xlabel("Epoch"); plt.legend(loc="center left")
    plt.tight_layout(); plt.savefig(PLOT_DIR/f"{key}_curves.png", dpi=150); plt.close()

plot_history("se",   "SE-ResNet18 Curves")
plot_history("mnv3", "MobileNetV3 Curves")

cm = confusion_matrix(y_true, y_pred_ens)
plt.figure(figsize=(4,4))
sns.heatmap(cm, annot=True, cmap="Blues", fmt="d",
            xticklabels=test_ds.classes, yticklabels=test_ds.classes)
plt.xlabel("Predicted"); plt.ylabel("True"); plt.title("Ensemble Confusion Matrix")
plt.tight_layout(); plt.savefig(PLOT_DIR/"ensemble_confusion_matrix.png", dpi=150); plt.close()

fpr, tpr, _ = roc_curve(y_true, prob_ens)
auc = roc_auc_score(y_true, prob_ens)
plt.figure(figsize=(4,4))
plt.plot(fpr, tpr, label=f"AUC={auc:.3f}")
plt.plot([0,1],[0,1],"--",color="gray")
plt.xlabel("FPR"); plt.ylabel("TPR"); plt.title("Ensemble ROC"); plt.legend()
plt.tight_layout(); plt.savefig(PLOT_DIR/"ensemble_roc.png", dpi=150); plt.close()

# ----------- 7.4 Corrected Grad-CAM block -----------
gradcam_dir = PLOT_DIR / "gradcam"
gradcam_dir.mkdir(exist_ok=True)

target_layers = [model_se.layer4[-1].se]           # use SE block
cam = GradCAM(model_se, target_layers)

# helper to undo normalization
denorm = transforms.Normalize(mean=[-m/s for m,s in zip(MEAN,STD)],
                              std=[1/s for s in STD])

samples_shown = 0
for idx, (tensor_img, label) in enumerate(islice(test_ds, 200)):
    if samples_shown >= 3:
        break
    if (label == 0 and samples_shown == 0) or label == 1:       # 1 normal, 2 pneumonia
        x = tensor_img.unsqueeze(0).to(DEVICE)                  # already normalized
        grayscale_cam = cam(x)[0]                               # H×W numpy

        # create RGB image for overlay
        vis = denorm(tensor_img.clone()).clamp(0,1)             # 1×H×W
        rgb = vis.repeat(3,1,1).permute(1,2,0).cpu().numpy()    # H×W×3
        overlay = show_cam_on_image(rgb, grayscale_cam, use_rgb=True)

        out_path = gradcam_dir / f"cam_{idx}_label{label}.png"
        plt.imsave(out_path, overlay)
        samples_shown += 1

print(f"\nAll plots saved under: {PLOT_DIR}")
