# train_pneumonia_hardcoded_commented.py
# =====================================
# Fully hardcoded training script for pneumonia detection using Chest X-Ray images.
# This version includes detailed comments explaining each block of code.

import os  # For filesystem operations (creating directories, path handling)
import numpy as np  # For numerical operations on arrays
import matplotlib.pyplot as plt  # For plotting confusion matrix & ROC curve
import seaborn as sns  # For enhanced visualizations of the confusion matrix
from tqdm import tqdm  # For progress bars during training/validation loops

import torch  # PyTorch core
import torch.nn as nn  # Neural network modules (layers, loss functions)
import torch.optim as optim  # Optimization algorithms (Adam, SGD, etc.)
import torchvision  # Vision utilities: models, datasets, transforms
from torchvision import transforms, datasets, models  # Specific torchvision submodules
from torch.utils.data import DataLoader  # For batching and shuffling datasets

from sklearn.metrics import (
    classification_report,  # Detailed precision/recall/F1 per class
    confusion_matrix,      # To compute the confusion matrix
    roc_auc_score,         # To compute the Area Under ROC Curve
    roc_curve              # To compute ROC curve points
)

# ====================================================
#                  H A R D-CODED SETTINGS
# ----------------------------------------------------
# All paths and hyperparameters are defined here so that
# no command-line arguments are necessary when running.
# ====================================================

# 1) Path to the main dataset directory containing `train`, `val`, and `test` subfolders:
DATA_DIR = r"C:\Users\<USER>\Desktop\projects\DNN\Chest X-Ray Images (Pneumonia)\chest_xray"

# 2) Training hyperparameters:
BATCH_SIZE    = 32     # Number of samples per gradient update
EPOCHS        = 10     # Total number of training epochs
LEARNING_RATE = 1e-4   # Initial learning rate for the optimizer
NUM_WORKERS   = 4      # Number of subprocesses for data loading (per DataLoader)

# 3) Device selection: automatically use GPU if available, else CPU
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 4) Directory to save generated plots (confusion matrix, ROC curve)
PLOT_DIR = r"C:\Users\<USER>\Desktop\projects\DNN\results\plots"

# 5) Filepath to save the best model checkpoint during training
CHECKPOINT_PATH = r"C:\Users\<USER>\Desktop\projects\DNN\results\best_model.pth"

# Ensure the directories for plots and checkpoints exist (create if missing)
os.makedirs(PLOT_DIR, exist_ok=True)
os.makedirs(os.path.dirname(CHECKPOINT_PATH), exist_ok=True)


def get_data_loaders(data_dir, batch_size, num_workers):
    """
    Prepare DataLoaders for training, validation, and testing.
    - Applies appropriate image transforms for each split.
    - Returns DataLoaders and the underlying datasets (for class labels).
    """
    # Mean and standard deviation for single-channel (grayscale) images
    MEAN = [0.485]
    STD  = [0.229]

    # Define image augmentations + preprocessing for training:
    train_tfms = transforms.Compose([
        transforms.Grayscale(num_output_channels=1),  # Convert RGB to 1-channel
        transforms.Resize((224, 224)),               # Resize to 224×224 for ResNet
        transforms.RandomHorizontalFlip(),           # Data augmentation
        transforms.RandomRotation(10),               # Small random rotations
        transforms.ToTensor(),                       # Convert PIL image to PyTorch Tensor
        transforms.Normalize(MEAN, STD),             # Normalize pixel values
    ])

    # Validation and test transforms: no augmentation, only resizing + normalization
    val_tfms = transforms.Compose([
        transforms.Grayscale(num_output_channels=1),
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(MEAN, STD),
    ])

    # Create ImageFolder datasets by scanning directory structure
    train_ds = datasets.ImageFolder(os.path.join(data_dir, "train"), transform=train_tfms)
    val_ds   = datasets.ImageFolder(os.path.join(data_dir, "val"),   transform=val_tfms)
    test_ds  = datasets.ImageFolder(os.path.join(data_dir, "test"),  transform=val_tfms)

    # Wrap datasets in DataLoaders for batching, shuffling, and parallel loading
    train_dl = DataLoader(
        train_ds,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True  # Speed up GPU transfers if using CUDA
    )
    val_dl = DataLoader(
        val_ds,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    test_dl = DataLoader(
        test_ds,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )

    # Return DataLoaders and datasets for later use (e.g., getting class names)
    return train_dl, val_dl, test_dl, train_ds, val_ds, test_ds


def build_model(device):
    """
    Load a pretrained ResNet-18 model, modify it for single-channel input,
    adjust the final layer for 2 output classes (Pneumonia vs Normal),
    and move it to the target device (CPU/GPU).
    """
    # Load ResNet-18 with pretrained weights on ImageNet
    model = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)

    # Modify the first convolution to accept 1-channel (instead of 3) input
    model.conv1 = nn.Conv2d(
        in_channels=1,   # Single-channel (grayscale)
        out_channels=64,
        kernel_size=7,
        stride=2,
        padding=3,
        bias=False
    )

    # Replace final fully-connected layer: 512 features → 2 classes
    num_features = model.fc.in_features
    model.fc = nn.Linear(num_features, 2)

    # Move model to the specified device (GPU if available)
    return model.to(device)


def train_one_epoch(model, dataloader, criterion, optimizer, device):
    """
    Train the model for one epoch over the training dataset.
    - Tracks running loss and accuracy.
    - Uses tqdm for a progress bar visual.
    """
    model.train()  # Set model to training mode (enables dropout, batchnorm updates)
    running_loss = 0.0
    correct = 0
    total = 0

    # tqdm loop for progress visualization
    for images, labels in tqdm(dataloader, desc="Training", leave=False):
        # Move data to the target device
        images = images.to(device, non_blocking=True)
        labels = labels.to(device, non_blocking=True)

        # Forward pass
        outputs = model(images)
        loss = criterion(outputs, labels)

        # Backpropagation and optimization step
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # Accumulate loss and correct predictions for reporting
        batch_size = images.size(0)
        running_loss += loss.item() * batch_size
        preds = outputs.argmax(dim=1)
        correct += (preds == labels).sum().item()
        total += batch_size

        # Update tqdm description with current loss and accuracy
        tqdm.write(f"Batch loss: {loss.item():.4f}, Acc: {(correct/total):.3%}")

    # Compute average loss and accuracy over the epoch
    avg_loss = running_loss / total
    accuracy = correct / total
    return avg_loss, accuracy


def validate_one_epoch(model, dataloader, criterion, device):
    """
    Evaluate model performance on the validation set for one epoch.
    - Behaves similarly to train but without weight updates.
    """
    model.eval()  # Set model to evaluation mode (disables dropout, batchnorm updates)
    running_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():  # Disable gradient computations
        for images, labels in tqdm(dataloader, desc="Validation", leave=False):
            images = images.to(device, non_blocking=True)
            labels = labels.to(device, non_blocking=True)

            outputs = model(images)
            loss = criterion(outputs, labels)

            # Update metrics
            batch_size = images.size(0)
            running_loss += loss.item() * batch_size
            preds = outputs.argmax(dim=1)
            correct += (preds == labels).sum().item()
            total += batch_size

    avg_loss = running_loss / total
    accuracy = correct / total
    return avg_loss, accuracy


def evaluate_test_set(model, dataloader, dataset, device, plot_dir):
    """
    After training, evaluate the final model on the test set.
    - Prints a classification report to stdout.
    - Generates and saves a confusion matrix plot.
    - Generates and saves an ROC curve plot with AUC score.
    """
    model.eval()
    y_true = []  # Ground-truth labels
    y_prob = []  # Predicted probabilities for the positive class

    with torch.no_grad():
        for images, labels in tqdm(dataloader, desc="Testing", leave=False):
            images = images.to(device, non_blocking=True)
            outputs = model(images)
            # Compute softmax probabilities and take probability of class "1"
            probs = torch.softmax(outputs, dim=1)[:, 1].cpu().numpy()
            y_prob.extend(probs)
            y_true.extend(labels.cpu().numpy())

    y_true = np.array(y_true)
    y_pred = (np.array(y_prob) > 0.5).astype(int)  # Threshold at 0.5 for binary preds

    # Print detailed classification metrics
    print("\n======== Test Set Classification Report ========")
    print(classification_report(y_true, y_pred, target_names=dataset.classes))

    # ----- Confusion Matrix -----
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(6, 5))
    sns.heatmap(
        cm,
        annot=True,
        fmt="d",
        cmap="Blues",
        xticklabels=dataset.classes,
        yticklabels=dataset.classes
    )
    plt.xlabel("Predicted Label")
    plt.ylabel("True Label")
    plt.title("Confusion Matrix")
    cm_path = os.path.join(plot_dir, "confusion_matrix.png")
    plt.savefig(cm_path, dpi=150)
    plt.close()
    print(f"Saved confusion matrix to: {cm_path}")

    # ----- ROC Curve & AUC -----
    fpr, tpr, _ = roc_curve(y_true, y_prob)
    auc_score = roc_auc_score(y_true, y_prob)

    plt.figure(figsize=(6, 5))
    plt.plot(fpr, tpr, label=f"AUC = {auc_score:.3f}")
    plt.plot([0, 1], [0, 1], linestyle="--", color="gray")  # Diagonal random guess line
    plt.xlabel("False Positive Rate")
    plt.ylabel("True Positive Rate")
    plt.title("ROC Curve")
    plt.legend(loc="lower right")
    roc_path = os.path.join(plot_dir, "roc_curve.png")
    plt.savefig(roc_path, dpi=150)
    plt.close()
    print(f"Saved ROC curve to: {roc_path}\n")


def main():
    """
    Main function:
    1) Checks dataset structure.
    2) Prepares DataLoaders.
    3) Builds and configures the model.
    4) Runs training and validation loops.
    5) Saves best-performing model checkpoint.
    6) Loads best model and evaluates on test set.
    """
    # 1) Verify that `train`, `val`, and `test` subfolders exist
    expected_subdirs = ["train", "val", "test"]
    for sub in expected_subdirs:
        folder_path = os.path.join(DATA_DIR, sub)
        if not os.path.isdir(folder_path):
            raise FileNotFoundError(
                f"Expected folder `{sub}` under `{DATA_DIR}`, but it was not found."
            )

    # 2) Create DataLoaders for all splits
    train_dl, val_dl, test_dl, train_ds, val_ds, test_ds = get_data_loaders(
        DATA_DIR, BATCH_SIZE, NUM_WORKERS
    )

    # 3) Instantiate model, loss function, and optimizer
    model = build_model(DEVICE)
    criterion = nn.CrossEntropyLoss()  # Suitable for multi-class (binary) classification
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    best_val_acc = 0.0  # Track best validation accuracy

    # 4) Training loop over the specified number of epochs
    for epoch in range(1, EPOCHS + 1):
        print(f"===== Epoch {epoch}/{EPOCHS} =====")
        train_loss, train_acc = train_one_epoch(model, train_dl, criterion, optimizer, DEVICE)
        print(f"Train   → loss: {train_loss:.4f}, acc: {train_acc:.3%}")

        val_loss, val_acc = validate_one_epoch(model, val_dl, criterion, DEVICE)
        print(f"Validate→ loss: {val_loss:.4f}, acc: {val_acc:.3%}")

        # 5) Save checkpoint if we achieve new best validation accuracy
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), CHECKPOINT_PATH)
            print(f"→ New best val accuracy: {best_val_acc:.3%}. Checkpoint saved to `{CHECKPOINT_PATH}`.")

    print(f"\nTraining complete. Best validation accuracy = {best_val_acc:.3%}")

    # 6) Load the best model checkpoint for final evaluation
    print("\nLoading best model for test evaluation...")
    model.load_state_dict(torch.load(CHECKPOINT_PATH, map_location=DEVICE))
    model = model.to(DEVICE)

    # 7) Evaluate on test set and generate reports/plots
    evaluate_test_set(model, test_dl, test_ds, DEVICE, PLOT_DIR)


if __name__ == "__main__":
    main()


##Output
Downloading: "https://download.pytorch.org/models/resnet18-f37072fd.pth" to C:\Users\<USER>\torch\hub\checkpoints\resnet18-f37072fd.pth
100%|██████████| 44.7M/44.7M [00:31<00:00, 1.47MB/s]
===== Epoch 1/10 =====
Training:   0%|          | 0/163 [00:00<?, ?it/s]c:\Users\<USER>\Desktop\projects\DNN\venv\Lib\site-packages\torch\utils\data\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.
  warnings.warn(warn_msg)
                                                                                  
Train   → loss: 0.1629, acc: 93.769%
                                                                                
Validate→ loss: 0.6194, acc: 68.750%
→ New best val accuracy: 68.750%. Checkpoint saved to `C:\Users\<USER>\Desktop\projects\DNN\results\best_model.pth`.
===== Epoch 2/10 =====
                                                                                   
Train   → loss: 0.0812, acc: 97.239%
                                                                               
Validate→ loss: 0.4828, acc: 75.000%
→ New best val accuracy: 75.000%. Checkpoint saved to `C:\Users\<USER>\Desktop\projects\DNN\results\best_model.pth`.
===== Epoch 3/10 =====
                                                                                   
Train   → loss: 0.0660, acc: 97.565%
                                                                                
Validate→ loss: 0.5967, acc: 68.750%
===== Epoch 4/10 =====
                                                                                   
Train   → loss: 0.0440, acc: 98.562%
                                                                                
Validate→ loss: 0.4490, acc: 68.750%
===== Epoch 5/10 =====
                                                                                   
Train   → loss: 0.0410, acc: 98.620%
                                                                                
Validate→ loss: 0.2208, acc: 87.500%
→ New best val accuracy: 87.500%. Checkpoint saved to `C:\Users\<USER>\Desktop\projects\DNN\results\best_model.pth`.
===== Epoch 6/10 =====
                                                                                   
Train   → loss: 0.0325, acc: 98.888%
                                                                                
Validate→ loss: 0.3992, acc: 81.250%
===== Epoch 7/10 =====
                                                                                   
Train   → loss: 0.0374, acc: 98.447%
                                                                                
Validate→ loss: 0.3125, acc: 87.500%
===== Epoch 8/10 =====
                                                                                   
Train   → loss: 0.0281, acc: 98.811%
                                                                               
Validate→ loss: 0.1900, acc: 93.750%
→ New best val accuracy: 93.750%. Checkpoint saved to `C:\Users\<USER>\Desktop\projects\DNN\results\best_model.pth`.
===== Epoch 9/10 =====
                                                                                   
Train   → loss: 0.0308, acc: 98.888%
                                                                                
Validate→ loss: 0.1535, acc: 87.500%
===== Epoch 10/10 =====
                                                                                   
Train   → loss: 0.0327, acc: 98.773%
                                                                                
Validate→ loss: 0.4878, acc: 87.500%

Training complete. Best validation accuracy = 93.750%

Loading best model for test evaluation...
                                                        

======== Test Set Classification Report ========
              precision    recall  f1-score   support

      NORMAL       0.98      0.65      0.78       234
   PNEUMONIA       0.82      0.99      0.90       390

    accuracy                           0.86       624
   macro avg       0.90      0.82      0.84       624
weighted avg       0.88      0.86      0.85       624

Saved confusion matrix to: C:\Users\<USER>\Desktop\projects\DNN\results\plots\confusion_matrix.png
Saved ROC curve to: C:\Users\<USER>\Desktop\projects\DNN\results\plots\roc_curve.png
