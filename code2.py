# train_pneumonia_ensemble.py
# ------------------------------------------------------------
# Chest-X-Ray Pneumonia detection
#   – SE-ResNet18  +  MobileNetV3-Small
#   – Soft-voting ensemble
#   – Hard-coded paths / hyper-params
#   – Generates: training curves, confusion matrix, ROC, Grad-CAMs
# ------------------------------------------------------------

import os, random, numpy as np, matplotlib.pyplot as plt, seaborn as sns
from itertools import islice
from pathlib import Path
from tqdm import tqdm

import torch, torch.nn as nn, torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import transforms, datasets, models
from sklearn.metrics import (classification_report, confusion_matrix,
                             roc_auc_score, roc_curve)

# Visual explanations
from pytorch_grad_cam import GradCAM
from pytorch_grad_cam.utils.image import show_cam_on_image

# -------------------------
# 1. HARD-CODED SETTINGS
# -------------------------
DATA_DIR   = Path(r"C:\Users\<USER>\Desktop\projects\DNN\Chest X-Ray Images (Pneumonia)\chest_xray")
RESULTS    = Path(r"C:\Users\<USER>\Desktop\projects\DNN\results")
PLOT_DIR   = RESULTS / "plots"
PLOT_DIR.mkdir(parents=True, exist_ok=True)

SE_CKPT    = RESULTS / "best_se_resnet18.pth"
MNV3_CKPT  = RESULTS / "best_mobilenetv3.pth"

BATCH_SIZE  = 32
EPOCHS      = 10
LR          = 1e-4
NUM_WORKERS = 4
DEVICE      = torch.device("cuda" if torch.cuda.is_available() else "cpu")
SEED        = 42
random.seed(SEED); np.random.seed(SEED); torch.manual_seed(SEED); torch.cuda.manual_seed_all(SEED)

# -------------------------
# 2. DATA LOADERS
# -------------------------
MEAN, STD = [0.485], [0.229]

train_tfms = transforms.Compose([
    transforms.Grayscale(1), transforms.Resize((224, 224)),
    transforms.RandomHorizontalFlip(), transforms.RandomRotation(10),
    transforms.ToTensor(), transforms.Normalize(MEAN, STD)
])
val_tfms = transforms.Compose([
    transforms.Grayscale(1), transforms.Resize((224, 224)),
    transforms.ToTensor(), transforms.Normalize(MEAN, STD)
])

train_ds = datasets.ImageFolder(DATA_DIR / "train", train_tfms)
val_ds   = datasets.ImageFolder(DATA_DIR / "val",   val_tfms)
test_ds  = datasets.ImageFolder(DATA_DIR / "test",  val_tfms)

train_dl = DataLoader(train_ds, BATCH_SIZE, shuffle=True,  num_workers=NUM_WORKERS, pin_memory=True)
val_dl   = DataLoader(val_ds,   BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS, pin_memory=True)
test_dl  = DataLoader(test_ds,  BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS, pin_memory=True)

# -------------------------
# 3. MODEL DEFINITIONS
# -------------------------
class SEBlock(nn.Module):
    """Squeeze-and-Excitation block."""
    def __init__(self, channels, r=16):
        super().__init__()
        self.pool = nn.AdaptiveAvgPool2d(1)
        self.fc   = nn.Sequential(
            nn.Linear(channels, channels // r), nn.ReLU(True),
            nn.Linear(channels // r, channels), nn.Sigmoid()
        )
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y

def se_resnet18():
    """ResNet-18 + SE blocks."""
    base = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
    base.conv1 = nn.Conv2d(1, 64, 7, 2, 3, bias=False)           # grayscale
    def _add_se(layer):
        for _, mod in layer.named_children():
            if isinstance(mod, models.resnet.BasicBlock):
                mod.add_module("se", SEBlock(mod.bn2.num_features))
    _add_se(base.layer1); _add_se(base.layer2); _add_se(base.layer3); _add_se(base.layer4)
    base.fc = nn.Linear(base.fc.in_features, 2)
    return base

def mobilenetv3_small():
    m = models.mobilenet_v3_small(weights=models.MobileNet_V3_Small_Weights.IMAGENET1K_V1)
    first = m.features[0][0]
    m.features[0][0] = nn.Conv2d(1, first.out_channels,
                                 kernel_size=first.kernel_size,
                                 stride=first.stride,
                                 padding=first.padding,
                                 bias=False)
    m.classifier[-1] = nn.Linear(m.classifier[-1].in_features, 2)
    return m

model_se   = se_resnet18().to(DEVICE)
model_mnv3 = mobilenetv3_small().to(DEVICE)

criterion = nn.CrossEntropyLoss()
opt_se    = optim.Adam(model_se.parameters(),   lr=LR)
opt_mnv3  = optim.Adam(model_mnv3.parameters(), lr=LR)

history = {k: {m: [] for m in ["train_loss","val_loss","train_acc","val_acc"]}
           for k in ["se","mnv3"]}
best_val_acc_se, best_val_acc_mnv3 = 0., 0.

# -------------------------
# 4. TRAIN / VAL UTILITIES
# -------------------------
def run_epoch(model, loader, train, optimizer=None):
    model.train() if train else model.eval()
    loss_sum, correct, total = 0., 0, 0
    with torch.set_grad_enabled(train):
        for xb, yb in loader:
            xb, yb = xb.to(DEVICE), yb.to(DEVICE)
            out = model(xb)
            loss = criterion(out, yb)
            if train:
                optimizer.zero_grad(); loss.backward(); optimizer.step()
            loss_sum += loss.item()*xb.size(0)
            preds = out.argmax(1)
            correct += (preds == yb).sum().item()
            total += xb.size(0)
    return loss_sum/total, correct/total

# -------------------------
# 5. TRAINING LOOP
# -------------------------
for epoch in range(1, EPOCHS+1):
    print(f"\nEpoch {epoch}/{EPOCHS}")
    # SE-ResNet18
    tl, ta = run_epoch(model_se, train_dl, True, opt_se)
    vl, va = run_epoch(model_se, val_dl,   False)
    for k,v in zip(["train_loss","train_acc","val_loss","val_acc"], [tl,ta,vl,va]):
        history["se"][k].append(v)
    if va > best_val_acc_se:
        best_val_acc_se = va; torch.save(model_se.state_dict(), SE_CKPT)
    # MobileNetV3
    tl, ta = run_epoch(model_mnv3, train_dl, True, opt_mnv3)
    vl, va = run_epoch(model_mnv3, val_dl,   False)
    for k,v in zip(["train_loss","train_acc","val_loss","val_acc"], [tl,ta,vl,va]):
        history["mnv3"][k].append(v)
    if va > best_val_acc_mnv3:
        best_val_acc_mnv3 = va; torch.save(model_mnv3.state_dict(), MNV3_CKPT)
    print(f"  SE-ResNet18  val_acc={history['se']['val_acc'][-1]:.3%}  best={best_val_acc_se:.3%}")
    print(f"  MobileNetV3  val_acc={history['mnv3']['val_acc'][-1]:.3%}  best={best_val_acc_mnv3:.3%}")

print("\nTraining complete. Reloading best checkpoints …")
model_se.load_state_dict(torch.load(SE_CKPT,  map_location=DEVICE))
model_mnv3.load_state_dict(torch.load(MNV3_CKPT, map_location=DEVICE))
model_se.eval(); model_mnv3.eval()

# -------------------------
# 6. TEST-TIME ENSEMBLE
# -------------------------
y_true, prob_se, prob_mnv3 = [], [], []
with torch.no_grad():
    for xb, yb in tqdm(test_dl, desc="Test"):
        xb = xb.to(DEVICE)
        prob_se.extend(torch.softmax(model_se(xb),1)[:,1].cpu().numpy())
        prob_mnv3.extend(torch.softmax(model_mnv3(xb),1)[:,1].cpu().numpy())
        y_true.extend(yb.numpy())

y_true  = np.array(y_true)
prob_se = np.array(prob_se)
prob_mnv3 = np.array(prob_mnv3)
prob_ens  = (prob_se + prob_mnv3)/2
y_pred_ens = (prob_ens > 0.5).astype(int)

print("\n=== Ensemble Classification Report ===")
print(classification_report(y_true, y_pred_ens, target_names=test_ds.classes))

# -------------------------
# 7. VISUALS
# -------------------------
def plot_history(key, title):
    ep = range(1, EPOCHS+1)
    plt.figure(figsize=(6,4))
    plt.plot(ep, history[key]["train_loss"], label="train_loss")
    plt.plot(ep, history[key]["val_loss"],   label="val_loss")
    plt.twinx()
    plt.plot(ep, history[key]["train_acc"],  "g--", label="train_acc")
    plt.plot(ep, history[key]["val_acc"],    "r--", label="val_acc")
    plt.title(title); plt.xlabel("Epoch"); plt.legend(loc="center left")
    plt.tight_layout(); plt.savefig(PLOT_DIR/f"{key}_curves.png", dpi=150); plt.close()

plot_history("se",   "SE-ResNet18 Curves")
plot_history("mnv3", "MobileNetV3 Curves")

cm = confusion_matrix(y_true, y_pred_ens)
plt.figure(figsize=(4,4))
sns.heatmap(cm, annot=True, cmap="Blues", fmt="d",
            xticklabels=test_ds.classes, yticklabels=test_ds.classes)
plt.xlabel("Predicted"); plt.ylabel("True"); plt.title("Ensemble Confusion Matrix")
plt.tight_layout(); plt.savefig(PLOT_DIR/"ensemble_confusion_matrix.png", dpi=150); plt.close()

fpr, tpr, _ = roc_curve(y_true, prob_ens)
auc = roc_auc_score(y_true, prob_ens)
plt.figure(figsize=(4,4))
plt.plot(fpr, tpr, label=f"AUC={auc:.3f}")
plt.plot([0,1],[0,1],"--",color="gray")
plt.xlabel("FPR"); plt.ylabel("TPR"); plt.title("Ensemble ROC"); plt.legend()
plt.tight_layout(); plt.savefig(PLOT_DIR/"ensemble_roc.png", dpi=150); plt.close()

# ----------- 7.4 Corrected Grad-CAM block -----------
gradcam_dir = PLOT_DIR / "gradcam"
gradcam_dir.mkdir(exist_ok=True)

target_layers = [model_se.layer4[-1].se]           # use SE block
cam = GradCAM(model_se, target_layers)

# helper to undo normalization
denorm = transforms.Normalize(mean=[-m/s for m,s in zip(MEAN,STD)],
                              std=[1/s for s in STD])

samples_shown = 0
for idx, (tensor_img, label) in enumerate(islice(test_ds, 200)):
    if samples_shown >= 3:
        break
    if (label == 0 and samples_shown == 0) or label == 1:       # 1 normal, 2 pneumonia
        x = tensor_img.unsqueeze(0).to(DEVICE)                  # already normalized
        grayscale_cam = cam(x)[0]                               # H×W numpy

        # create RGB image for overlay
        vis = denorm(tensor_img.clone()).clamp(0,1)             # 1×H×W
        rgb = vis.repeat(3,1,1).permute(1,2,0).cpu().numpy()    # H×W×3
        overlay = show_cam_on_image(rgb, grayscale_cam, use_rgb=True)

        out_path = gradcam_dir / f"cam_{idx}_label{label}.png"
        plt.imsave(out_path, overlay)
        samples_shown += 1

print(f"\nAll plots saved under: {PLOT_DIR}")

#Output

Epoch 1/10
c:\Users\<USER>\Desktop\projects\DNN\venv\Lib\site-packages\torch\utils\data\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.
  warnings.warn(warn_msg)
  SE-ResNet18  val_acc=68.750%  best=68.750%
  MobileNetV3  val_acc=50.000%  best=50.000%

Epoch 2/10
  SE-ResNet18  val_acc=56.250%  best=68.750%
  MobileNetV3  val_acc=50.000%  best=50.000%

Epoch 3/10
  SE-ResNet18  val_acc=68.750%  best=68.750%
  MobileNetV3  val_acc=56.250%  best=56.250%

Epoch 4/10
  SE-ResNet18  val_acc=75.000%  best=75.000%
  MobileNetV3  val_acc=75.000%  best=75.000%

Epoch 5/10
  SE-ResNet18  val_acc=81.250%  best=81.250%
  MobileNetV3  val_acc=68.750%  best=75.000%

Epoch 6/10
  SE-ResNet18  val_acc=68.750%  best=81.250%
  MobileNetV3  val_acc=93.750%  best=93.750%

Epoch 7/10
  SE-ResNet18  val_acc=93.750%  best=93.750%
...
  SE-ResNet18  val_acc=87.500%  best=93.750%
  MobileNetV3  val_acc=68.750%  best=93.750%

Training complete. Reloading best checkpoints …
Output is truncated. View as a scrollable element or open in a text editor. Adjust cell output settings...
Test: 100%|██████████| 20/20 [00:15<00:00,  1.28it/s]

=== Ensemble Classification Report ===
              precision    recall  f1-score   support

      NORMAL       0.97      0.64      0.77       234
   PNEUMONIA       0.82      0.99      0.90       390

    accuracy                           0.86       624
   macro avg       0.89      0.81      0.83       624
weighted avg       0.88      0.86      0.85       624
